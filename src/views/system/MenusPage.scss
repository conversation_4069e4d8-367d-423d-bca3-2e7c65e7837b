/* 菜单管理页面样式 */

.menu-page {
  padding: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.toolbar {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 表格容器样式 - 优化虚拟滚动 */
.table-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fff;
  min-height: 0;
  /* 确保flex子项可以收缩 */
  overflow: hidden;
  width: 100%;
}

.data-table-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

/* 虚拟滚动表格样式优化 */
:deep(.n-data-table) {
  width: 100% !important;

  /* 表头样式 */
  .n-data-table-base-table-header {
    .n-data-table-th {
      font-weight: 600;
      text-align: center !important;

      .n-data-table-th__title {
        justify-content: center;
      }
    }
  }

  /* 虚拟滚动容器样式 */
  .n-data-table-base-table-body {
    overflow: auto;
  }

  /* 确保虚拟滚动表格不出现额外的空白列 */
  .n-data-table-base-table {
    width: 100% !important;
    table-layout: fixed;
    border-collapse: collapse;
  }

  /* 虚拟滚动行样式 */
  .n-data-table-tr {
    height: 48px;
  }

  /* 数据单元格样式 */
  .n-data-table-td {
    text-align: center !important;
  }

  /* 修复虚拟滚动表格列之间的空白区域问题 */
  .n-data-table-base-table-header,
  .n-data-table-base-table-body {

    .n-data-table-th,
    .n-data-table-td {
      border-right: 1px solid var(--n-border-color);
      box-sizing: border-box;

      &:last-child {
        border-right: none;
      }
    }
  }

  /* 确保固定列的边框连续性 */
  .n-data-table-th--fixed-left,
  .n-data-table-td--fixed-left {
    border-right: 1px solid var(--n-border-color) !important;
  }

  /* 修复空白列问题 */
  .n-data-table-base-table {

    .n-data-table-th,
    .n-data-table-td {
      &:not([data-col-key]) {
        display: none !important;
      }
    }
  }

  /* 确保表格内容区域填满容器 */
  .n-data-table-wrapper {
    width: 100% !important;
  }

  /* 修复表格右侧空白问题 */
  .n-data-table-base-table-body-wrapper {
    width: 100% !important;
    overflow-x: auto;
  }

  /* 确保表格能够横向滚动 */
  .n-scrollbar-container {
    width: 100% !important;
  }

  /* 优化固定列显示 */
  .n-data-table-th--fixed-left,
  .n-data-table-td--fixed-left {
    background-color: #fafafa !important;
    z-index: 2;
  }
}

/* 菜单名称列的特殊处理，保持左对齐但考虑缩进 */
:deep(.n-data-table-td .n-data-table-td__content span) {
  text-align: left;
  width: 100%;
}

/* 为菜单名称列添加新的样式 */
:deep(.n-data-table-td[data-col-key="menuLabel"] .n-data-table-td__content) {
  justify-content: flex-start; /* 左对齐内容 */
}

/* 添加以下样式来增加图标之间的间距 */
:deep(.n-icon) {
  margin: 0 2px;
}

/* 为操作栏添加新的样式 */
:deep(.n-data-table-td[data-col-key="actions"] .n-space) {
  gap: 4px !important;
}

/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}
