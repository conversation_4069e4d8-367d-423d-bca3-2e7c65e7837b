<template>
  <div class="menu-page">
    <n-space class="toolbar">
      <n-button type="primary" @click="refreshCache" round>
        <template #icon>
          <n-icon><refresh-outline /></n-icon>
        </template>
        刷新数据
      </n-button>
      <n-button type="success" @click="showAddRootMenu" round>
        <template #icon>
          <n-icon><add-outline /></n-icon>
        </template>
        新增菜单
      </n-button>
      <n-button type="error" @click="batchDelete" round>
        <template #icon>
          <n-icon><trash-outline /></n-icon>
        </template>
        选中删除
      </n-button>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="menuTable"
          :columns="columns"
          :data="menuList"
          :row-key="(row) => row.id"
          :children-key="'children'"
          :expanded-row-keys="expandedKeys"
          :max-height="tableMaxHeight"
          virtual-scroll
          virtual-scroll-x
          :scroll-x="scrollX"
          :min-row-height="48"
          :height-for-row="() => 48"
          virtual-scroll-header
          :header-height="48"
          striped
          size="medium"
          @update:expanded-row-keys="handleExpand"
          @update:checked-row-keys="handleCheck"
        >
          <template #empty>
            <span>暂无数据</span>
          </template>
        </n-data-table>
      </div>
    </div>

    <n-modal
      v-model:show="dialogVisible"
      :title="dialogTitle"
      preset="card"
      style="width: 30%"
    >
      <n-form :model="menuForm" label-placement="left" label-width="100px">
        <n-form-item label="父菜单" v-if="menuForm.parentId">
          <n-input v-model:value="parentMenuName" disabled />
        </n-form-item>
        <n-form-item label="菜单名称">
          <n-input v-model:value="menuForm.menuLabel" />
        </n-form-item>
        <n-form-item label="节点路由">
          <n-input v-model:value="menuForm.menuPath" />
        </n-form-item>
        <n-form-item label="是否显示">
          <n-switch v-model:value="menuForm.visible" />
        </n-form-item>
        <n-form-item label="文件路径">
          <n-input v-model:value="menuForm.viewPath" />
        </n-form-item>
        <n-form-item label="节点图标">
          <n-input v-model:value="menuForm.menuIcon" />
        </n-form-item>
        <n-form-item label="序号">
          <n-input-number v-model:value="menuForm.menuOrder" :min="1" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="dialogVisible = false">取消</n-button>
          <n-button type="primary" @click="saveMenu">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, h, computed } from "vue";
import {
  NButton,
  NSpace,
  NSwitch,
  NDataTable,
  useDialog,
  NIcon,
} from "naive-ui";
import {
  RefreshOutline,
  AddOutline,
  TrashOutline,
  PencilOutline,
} from "@vicons/ionicons5";
import { menuApi } from "@/api/menus";
import message from "@/utils/messages";

const menuList = ref([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const menuForm = ref({
  id: null,
  menuLabel: "",
  menuPath: "",
  visible: true,
  viewPath: "",
  menuIcon: "",
  menuOrder: 1,
  parentId: null,
});

const selectedMenus = ref([]);
const selectedParentMenu = ref(null);
const parentMenuName = ref("");

const menuTable = ref(null);

const expandedKeys = ref([]);
const windowHeight = ref(window.innerHeight);

const dialog = useDialog();

// 计算表格最大高度 - 用于虚拟滚动
const tableMaxHeight = computed(() => {
  const screenHeight = windowHeight.value;

  // 基础组件高度配置
  const pagepadding = 32; // 页面上下padding
  const toolbarHeight = 80; // 工具栏高度
  const margin = 20; // 额外边距

  // 计算表格容器的可用高度
  const calculatedHeight = screenHeight - pagepadding - toolbarHeight - margin;

  // 根据屏幕尺寸进行优化
  let finalHeight;
  if (screenHeight >= 1440) {
    // 大屏幕 (27寸+)
    finalHeight = Math.max(calculatedHeight, 700);
  } else if (screenHeight >= 1080) {
    // 24寸屏幕
    finalHeight = Math.max(calculatedHeight, 500);
  } else if (screenHeight >= 768) {
    // 中等屏幕
    finalHeight = Math.max(calculatedHeight, 400);
  } else {
    // 小屏幕 (13寸等)
    finalHeight = Math.max(calculatedHeight, 300);
  }
  return finalHeight;
});

// 计算所有列的总宽度 - 用于虚拟滚动的scroll-x配置
const scrollX = computed(() => {
  // 使用 reduce 计算所有列的宽度总和
  return columns.reduce((sum, col) => sum + (col.width || 0), 0);
});

function createColumns() {
  return [
    { type: "selection", width: 50, align: "center", fixed: "left" },
    {
      title: "菜单名称",
      key: "menuLabel",
      width: 300,
      align: "left",
      fixed: "left",
      render(row) {
        const indent = row.level * 20;
        return h(
          "span",
          { style: `padding-left: ${indent}px;` },
          row.menuLabel
        );
      },
    },
    {
      title: "图标",
      key: "menuIcon",
      width: 120,
      align: "center",
      render(row) {
        return h("div", { innerHTML: row.menuIcon });
      },
    },
    { title: "节点路由", key: "menuPath", width: 300, align: "center" },
    { title: "文件路径", key: "viewPath", width: 350, align: "center" },
    { title: "序号", key: "menuOrder", width: 100, align: "center" },
    {
      title: "是否显示",
      key: "visible",
      width: 120,
      align: "center",
      render(row) {
        return h(NSwitch, {
          value: row.visible,
          loading: updatingVisibility.value.has(row.id),
          onUpdateValue: (value) => toggleVisibility(row, value),
        });
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 160,
      align: "center",
      render(row) {
        return h(NSpace, { justify: "center", size: "small" }, () => [
          h(NIcon, {
            component: AddOutline,
            size: 20,
            color: "#18a058",
            style: {
              cursor: "pointer",
            },
            onClick: () => addSubMenu(row),
          }),
          h(NIcon, {
            component: PencilOutline,
            size: 20,
            color: "#2080f0",
            style: {
              cursor: "pointer",
            },
            onClick: () => editMenu(row),
          }),
          h(NIcon, {
            component: TrashOutline,
            size: 20,
            color: "#d03050",
            style: {
              cursor: "pointer",
            },
            onClick: () => deleteMenu(row),
          }),
        ]);
      },
    },
  ];
}

const columns = createColumns();

const updatingVisibility = ref(new Set()); // 用于跟踪正在更新可见性的行

// 窗口大小变化处理
const handleResize = () => {
  windowHeight.value = window.innerHeight;
};

onMounted(async () => {
  await fetchMenus();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

async function fetchMenus() {
  const response = await menuApi.getMenus();
  menuList.value = buildMenuTree(response.data);
}

function buildMenuTree(menus) {
  const map = new Map();
  const tree = [];

  // 首先创建所有节点的映射
  menus.forEach((menu) => {
    map.set(menu.id, { ...menu, children: [], level: 0 });
  });

  // 然后构建树形结构
  menus.forEach((menu) => {
    const node = map.get(menu.id);
    if (menu.parentId === 1) {
      tree.push(node);
    } else {
      const parent = map.get(menu.parentId);
      if (parent) {
        parent.children.push(node);
        node.level = parent.level + 1; // 设置层级
      }
    }
  });

  return tree;
}

function refreshCache() {
  // 实现刷新缓存的逻辑
  message.success("缓存已刷新");
}

function showAddRootMenu() {
  dialogTitle.value = "新增菜单";
  menuForm.value = {
    id: null,
    menuLabel: "",
    menuPath: "",
    visible: true,
    viewPath: "",
    menuIcon: "",
    menuOrder: 1,
    parentId: 1,
  };
  parentMenuName.value = "系统菜单";
  dialogVisible.value = true;
}

function addSubMenu(row) {
  dialogTitle.value = "新增子菜单";
  menuForm.value = {
    id: null,
    menuLabel: "",
    menuPath: "",
    visible: true,
    viewPath: "",
    menuIcon: "",
    menuOrder: 1,
    parentId: row.id,
  };
  parentMenuName.value = row.menuLabel;
  dialogVisible.value = true;
}

function editMenu(row) {
  dialogTitle.value = "编辑菜单";
  menuForm.value = { ...row };
  dialogVisible.value = true;
}

async function saveMenu() {
  if (menuForm.value.id) {
    await menuApi.updateMenu(menuForm.value);
    message.success("菜单更新成功");
  } else {
    await menuApi.createMenu(menuForm.value);
    message.success("菜单创建成功");
  }
  dialogVisible.value = false;
  await fetchMenus();
}

async function batchDelete() {
  if (selectedMenus.value.length === 0) {
    message.warning("请先选择要删除的菜单");
    return;
  }
  dialog.warning({
    title: "警告",
    content: `确定要删除选中的 ${selectedMenus.value.length} 个菜单吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      const ids = selectedMenus.value.map((menu) => menu.id);
      await deleteMenus(ids);
    },
  });
}

async function deleteMenu(row) {
  dialog.warning({
    title: "警告",
    content: "确定要删除该菜单？",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      await deleteMenus([row.id]);
    },
  });
}

async function deleteMenus(ids) {
  try {
    await menuApi.deleteMenus(ids);
    message.success("菜单删除成功");
    await fetchMenus();
  } catch (error) {
    message.error("删除菜单失败");
    console.error("删除菜单时出错:", error);
  }
}

async function toggleVisibility(row, value) {
  updatingVisibility.value.add(row.id); // 开始更新，添加到集合中
  try {
    await menuApi.updateMenu({ ...row, visible: value });
    message.success("菜单可见性已更新");
    await fetchMenus(); // 在成功更新后刷新列表
  } catch (error) {
    message.error("更新菜单可见性失败");
    console.error("更新菜单可见性时出错:", error);
  } finally {
    updatingVisibility.value.delete(row.id); // 更新完成，从集合中移除
  }
}

function handleCheck(checkedKeys) {
  selectedMenus.value = checkedKeys;
  selectedParentMenu.value = checkedKeys.length === 1 ? checkedKeys[0] : null;
}

function handleExpand(keys) {
  expandedKeys.value = keys;
}
</script>

<style lang="scss" scoped>
@use "./MenusPage.scss";
</style>
